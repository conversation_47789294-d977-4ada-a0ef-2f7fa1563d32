"""
CatchUp2 State Management

This module defines the state management system for CatchUp2, adapting the original
CatchUp state to work with the research_agent architecture while maintaining
backward compatibility and all existing functionality.

Key Features:
- Compatible with research_agent's DeepAgentState
- Maintains CatchUp's multi-tenant marketplace context
- Enhanced memory management with intelligent message prioritization
- User context and session management
- Conversation tracking and metrics
"""

from typing import TypedDict, Annotated, Optional, List, Dict, Any, Union
from typing_extensions import Required
from langchain_core.messages import AnyMessage

from deepagents.state import DeepAgentState
from shared.logger import logger


def enhanced_memory_reducer(existing: List[AnyMessage], new: List[AnyMessage]) -> List[AnyMessage]:
    """
    Enhanced reducer with intelligent message prioritization for conversation memory management.

    This function implements sophisticated memory management for long-running conversations
    in the multi-tenant marketplace environment. It ensures that important context is
    preserved while staying within memory constraints for optimal performance.

    Business Logic:
    - Prioritizes recent messages for immediate context continuity
    - Preserves system messages that contain important configuration and instructions
    - Retains successful tool results that provide valuable marketplace data
    - Keeps user preference statements for personalization
    - Maintains chronological order for conversation flow

    Memory Management Strategy:
    1. Recent messages (last 5 or half of budget) - immediate context
    2. System messages - configuration and instructions
    3. Successful tool results - marketplace data and booking information
    4. User preference statements - personalization context
    5. Deduplication to prevent memory waste

    Integration Points:
    - Used by CatchUp2State for automatic message list management
    - Supports conversation continuity across multiple tool calls
    - Enables context-aware responses in marketplace scenarios
    - Optimizes token usage for cost-effective operations

    Args:
        existing: Previously stored messages in conversation history
        new: New messages to add to the conversation

    Returns:
        List of messages within memory budget, intelligently prioritized
    """
    # Handle edge cases for empty message lists
    if not existing:
        return new

    if not new:
        return existing

    # Combine all messages for processing
    combined = existing + new

    # Extract memory budget from message metadata or use sensible default
    # This allows dynamic memory management based on conversation complexity
    memory_budget = 15  # Default budget for marketplace conversations
    if new and hasattr(new[-1], 'additional_kwargs'):
        memory_budget = new[-1].additional_kwargs.get('memory_budget', 15)

    # If within budget, return all messages without processing
    if len(combined) <= memory_budget:
        return combined

    logger.info(f"Memory budget exceeded ({len(combined)} > {memory_budget}), applying intelligent trimming")

    # Initialize containers for different message priorities
    important_messages = []  # High-priority messages from earlier conversation
    recent_messages = []     # Recent messages for immediate context

    # Strategy 1: Always preserve recent messages for conversation continuity
    # Recent messages are critical for understanding current user intent and context
    recent_count = min(5, memory_budget // 2)  # Reserve half budget for recent messages
    recent_messages = combined[-recent_count:]
    logger.debug(f"Preserving {len(recent_messages)} recent messages")

    # Strategy 2: Selectively preserve important messages from earlier conversation
    # This maintains long-term context while respecting memory constraints
    remaining_budget = memory_budget - len(recent_messages)

    for msg in combined[:-recent_count]:  # Process older messages
        if remaining_budget <= 0:
            break

        # Priority 1: System messages contain critical configuration and instructions
        if hasattr(msg, 'type') and msg.type == 'system':
            important_messages.append(msg)
            remaining_budget -= 1
            logger.debug("Preserved system message")

        # Priority 2: Successful tool results contain valuable marketplace data
        elif hasattr(msg, 'type') and msg.type == 'tool' and 'error' not in msg.content.lower():
            important_messages.append(msg)
            remaining_budget -= 1
            logger.debug("Preserved successful tool result")

        # Priority 3: User preference statements enable personalization
        elif hasattr(msg, 'content') and any(keyword in msg.content.lower()
                                           for keyword in ['my name is', 'i prefer', 'i like', 'i need']):
            important_messages.append(msg)
            remaining_budget -= 1
            logger.debug("Preserved user preference statement")

    # Strategy 3: Combine preserved messages maintaining chronological order
    # This ensures conversation flow remains logical and coherent
    result = important_messages + recent_messages

    # Strategy 4: Remove duplicates while preserving order
    # This prevents memory waste from duplicate message references
    seen = set()
    deduplicated = []
    for msg in result:
        msg_id = id(msg)  # Use object identity for deduplication
        if msg_id not in seen:
            seen.add(msg_id)
            deduplicated.append(msg)

    logger.info(f"Memory trimmed from {len(combined)} to {len(deduplicated)} messages")
    logger.debug(f"Preserved {len(important_messages)} important + {len(recent_messages)} recent messages")

    return deduplicated


class CatchUp2State(DeepAgentState):
    """
    Simplified state for CatchUp2 agent that extends DeepAgentState.

    This state maintains the original CatchUp agent's simplicity while extending
    DeepAgentState for compatibility with the research_agent architecture.
    It provides essential marketplace context without unnecessary complexity.

    Key Features:
    - Extends DeepAgentState for research_agent compatibility
    - Maintains original CatchUp field structure for backward compatibility
    - Includes intelligent memory management for marketplace conversations
    - Supports both chat mode (markdown) and API mode (JSON) responses
    """

    # Enhanced conversation management with intelligent memory (inherited from DeepAgentState)
    messages: Annotated[List[AnyMessage], enhanced_memory_reducer]

    # Essential user and session context (matching original CatchUp structure)
    user_id: Required[str]
    session_id: Required[str]
    email_address: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    memory_length: Optional[str] = None  # Keep as string to match original CatchUp
    isChat: Optional[bool] = True  # Whether response should be markdown (True) or JSON (False)


def create_catchup2_state(
    user_id: str,
    session_id: str,
    email_address: Optional[str] = None,
    latitude: Optional[float] = None,
    longitude: Optional[float] = None,
    memory_length: Optional[int] = None,
    is_chat: bool = True
) -> CatchUp2State:
    """
    Create a new CatchUp2State instance with simplified structure.

    This function creates a CatchUp2State that maintains the original CatchUp
    agent's field structure while extending DeepAgentState for research_agent
    compatibility.

    Args:
        user_id: Unique user identifier
        session_id: Unique session identifier
        email_address: User's email address
        latitude: User's latitude coordinate (converted to string)
        longitude: User's longitude coordinate (converted to string)
        memory_length: Memory budget for conversation (converted to string)
        is_chat: Whether responses should be markdown (True) or JSON (False)

    Returns:
        Initialized CatchUp2State instance with simplified structure
    """
    return CatchUp2State(
        messages=[],
        user_id=user_id,
        session_id=session_id,
        email_address=email_address,
        latitude=str(latitude) if latitude is not None else None,
        longitude=str(longitude) if longitude is not None else None,
        memory_length=str(memory_length) if memory_length is not None else None,
        isChat=is_chat
    )
