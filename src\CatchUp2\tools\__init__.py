"""
CatchUp2 Tools Module

This module contains all tools used by the CatchUp2 agent for marketplace operations,
user management, and communication. Tools are synchronized with CatchUp project
for SQL query consistency and database interaction patterns.

Tool Categories:
- Marketplace: Deal search, category management (synchronized with CatchUp)
- User Management: User profiles (synchronized with CatchUp)
- Communication: Email and WhatsApp messaging (uses MCP tools)
- Booking: Reservation creation and management (uses MCP tools)
"""

# Import synchronized tools from their respective modules
from .marketplace_tools import get_all_categories, search_deals, get_deals_by_categoryId, get_deals
from .user_tools import get_user_details_by_id

__all__ = [
    # Synchronized marketplace tools (matching CatchUp)
    'get_all_categories',
    'search_deals',
    'get_deals_by_categoryId',  # Note: using CatchUp naming convention
    'get_deals',

    # Synchronized user tools (matching CatchUp)
    'get_user_details_by_id',

    # MCP tools (imported dynamically in agent):
    # 'get_chat_history', 'get_business_details', 'get_booking_details',
    # 'create_booking', 'sent_email_to_users', 'whatsapps_sent_tool'
]
