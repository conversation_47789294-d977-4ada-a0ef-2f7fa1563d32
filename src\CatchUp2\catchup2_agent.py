"""
CatchUp2 Agent - Main Implementation

This module implements the core CatchUp2 agent using the research_agent architecture
while maintaining all functionality of the original CatchUp system for multi-tenant
marketplace customer service.

Key Features:
- Built on research_agent pattern using create_deep_agent
- Specialized sub-agents for marketplace operations
- Multi-tenant marketplace support with business isolation
- Enhanced query processing and intent detection
- Comprehensive tool integration for deals, bookings, and communication
- JSON response formatting with required fields (llmResponse, llmResponseIntent, userIntent, relatedIds)

Architecture:
- Main agent handles overall conversation flow and coordination
- Sub-agents handle specialized tasks (booking, deal search, communication, user management)
- State management compatible with research_agent while preserving CatchUp functionality
- Tool integration through deepagents framework
"""

import os
import asyncio
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from langchain_core.tools import BaseTool, tool

# Import research_agent components
from deepagents import create_deep_agent
from deepagents.tools import write_todos
from shared.llm_factory import create_llm
from shared.logger import logger

# Import CatchUp2 components
from CatchUp2.prompts import create_catchup2_instructions
from CatchUp2.sub_agents import get_all_catchup2_sub_agents
from CatchUp2.state import CatchUp2State, create_catchup2_state
from CatchUp2.tools.marketplace_tools import (
    get_all_categories,
    search_deals,
    get_deals_by_categoryId,  # Synchronized with CatchUp naming
    get_deals
)
from CatchUp2.tools.user_tools import (
    get_user_details_by_id
)

# Import MCP tools functionality (same as CatchUp)
from shared.mcp_tools import get_catchup_tools_by_names

# Load environment variables
load_dotenv()

# MCP tools used in CatchUp (synchronized with CatchUp project)
MCP_TOOLS = [
    "get_chat_history",
    "get_business_details",
    "get_booking_details",
    "create_booking",
    "sent_email_to_users",
    "whatsapps_sent_tool",
]

# Global cache for MCP tools to avoid repeated loading
_mcp_tools_cache: Optional[List[BaseTool]] = None


def create_mcp_placeholder_tool(tool_name: str) -> BaseTool:
    """
    Create a placeholder tool that loads the actual MCP tool when called.

    This allows us to create the agent synchronously while still having access
    to MCP tools at runtime.

    Args:
        tool_name: Name of the MCP tool to create a placeholder for

    Returns:
        A placeholder tool that will resolve to the actual MCP tool when called
    """
    # Set the description based on the tool name
    descriptions = {
        "get_chat_history": "Retrieve chat history for a user or session",
        "get_business_details": "Get detailed information about a business/merchant",
        "get_booking_details": "Retrieve booking information and details",
        "create_booking": "Create a new booking for a user",
        "sent_email_to_users": "Send email notifications to users",
        "whatsapps_sent_tool": "Send WhatsApp messages to users",
    }

    description = descriptions.get(tool_name, f"MCP tool: {tool_name}")

    @tool(description=description)
    async def placeholder_tool(*args, **kwargs) -> str:
        """Placeholder tool that loads MCP tool dynamically."""
        global _mcp_tools_cache

        # Load MCP tools if not cached
        if _mcp_tools_cache is None:
            try:
                _mcp_tools_cache = await get_catchup_tools_by_names(MCP_TOOLS)
                logger.info(f"Loaded {len(_mcp_tools_cache)} MCP tools dynamically")
            except Exception as e:
                logger.error(f"Failed to load MCP tools dynamically: {e}")
                return f"Error: MCP tool '{tool_name}' is not available. {str(e)}"

        # Find the actual tool
        actual_tool = None
        for tool in _mcp_tools_cache:
            if tool.name == tool_name:
                actual_tool = tool
                break

        if actual_tool is None:
            return f"Error: MCP tool '{tool_name}' not found in loaded tools"

        # Call the actual tool asynchronously
        try:
            # For MCP tools, we need to pass the arguments properly
            # Most MCP tools expect a single input parameter
            if args:
                # If positional args are provided, use the first one
                result = await actual_tool.ainvoke(args[0] if len(args) == 1 else {"args": args})
            elif kwargs:
                # If keyword args are provided, use them directly
                result = await actual_tool.ainvoke(kwargs)
            else:
                # No arguments provided
                result = await actual_tool.ainvoke({})

            return str(result)
        except Exception as e:
            logger.error(f"Error calling MCP tool '{tool_name}': {e}")
            return f"Error calling MCP tool '{tool_name}': {str(e)}"

    # Manually set the tool name
    placeholder_tool.name = tool_name
    return placeholder_tool


def create_mcp_placeholder_tools() -> List[BaseTool]:
    """
    Create placeholder tools for all MCP tools.

    Returns:
        List of placeholder tools that will resolve to actual MCP tools when called
    """
    placeholder_tools = []
    for tool_name in MCP_TOOLS:
        placeholder_tool = create_mcp_placeholder_tool(tool_name)
        placeholder_tools.append(placeholder_tool)

    logger.info(f"Created {len(placeholder_tools)} MCP placeholder tools")
    return placeholder_tools


async def create_catchup2_tools() -> List:
    """
    Create and return all tools available to the CatchUp2 agent.

    This function assembles all marketplace, user management, and communication
    tools that the agent can use to handle customer service operations in the
    multi-tenant marketplace environment.

    Tool Categories and Business Logic:

    1. Marketplace Tools - Core business operations
       - Category management for service discovery
       - Deal search and filtering with availability checking
       - Booking creation and management with validation
       - Business information for location and contact details

    2. User Management Tools - Customer relationship management
       - User profile access for personalization
       - Preference management for customized experiences
       - Booking history for customer service context
       - Location services for proximity-based recommendations

    3. Communication Tools - Multi-channel customer engagement
       - Email services for formal communications and confirmations
       - WhatsApp integration for instant notifications
       - Preference management for communication channels
       - Privacy-compliant messaging with user consent

    Integration Points:
    - Tools are bound to the LLM after agent creation for optimal performance
    - Each tool maintains consistent JSON response format for parsing
    - Error handling is standardized across all tools for reliability
    - Logging is integrated for monitoring and debugging
    - Database operations use Supabase client for data consistency

    Returns:
        List of all available tools for the CatchUp2 agent, organized by category
    """
    # Load MCP tools (same as CatchUp)
    mcp_tools = await get_catchup_tools_by_names(MCP_TOOLS)
    logger.info(f"Loaded {len(mcp_tools)} MCP tools: {[tool.name for tool in mcp_tools]}")

    # Core research_agent tools for planning and task management
    # The write_todos tool is essential for the research_agent pattern and enables
    # automatic TODO list creation for complex multi-step marketplace operations
    planning_tools = [
        write_todos              # TODO list creation and task management (from deepagents)
    ]

    # Marketplace tools for core business operations (synchronized with CatchUp)
    # These tools handle the primary marketplace functionality including
    # deal discovery and category management using identical SQL patterns
    local_marketplace_tools = [
        get_all_categories,      # Category discovery and validation (synchronized)
        search_deals,            # Deal search by category name (synchronized)
        get_deals_by_categoryId, # Direct category-based deal retrieval (synchronized)
        get_deals,               # General deal access with business filtering (synchronized)
    ]

    # User management tools for customer relationship management (synchronized with CatchUp)
    # These tools provide user data access using identical SQL patterns
    local_user_tools = [
        get_user_details_by_id,  # User profile information (synchronized)
    ]

    # Combine local tools
    local_tools = planning_tools + local_marketplace_tools + local_user_tools

    # Combine all tools: local tools + MCP tools (same pattern as CatchUp)
    all_tools = mcp_tools + local_tools

    # Log tool configuration for monitoring and debugging
    logger.info(f"Created {len(all_tools)} tools for CatchUp2 agent")
    logger.debug(f"MCP tools: {[tool.name for tool in mcp_tools]}")
    logger.debug(f"Planning tools: {[tool.name for tool in planning_tools]}")
    logger.debug(f"Local marketplace tools: {[tool.name for tool in local_marketplace_tools]}")
    logger.debug(f"Local user tools: {[tool.name for tool in local_user_tools]}")

    return all_tools


async def create_catchup2_agent(
    model_name: str = "anthropic/claude-3.5-sonnet",
    **kwargs
):
    """
    Create the main CatchUp2 agent using the research_agent architecture.

    This function creates a comprehensive customer service agent for the multi-tenant
    marketplace platform, incorporating all necessary tools and sub-agents for
    handling complex customer service operations.

    Args:
        model_name: Name of the LLM model to use
        **kwargs: Additional configuration parameters

    Returns:
        Configured CatchUp2 agent ready for customer service operations
    """
    logger.info(f"Creating CatchUp2 agent with model: {model_name}")

    # Create the LLM instance
    llm = create_llm(model_name, **kwargs)
    logger.info("LLM instance created successfully")

    # Get all tools for the agent (including MCP tools)
    tools = await create_catchup2_tools()
    logger.info(f"Loaded {len(tools)} tools for CatchUp2 agent")
    
    # Get all sub-agents for specialized operations
    sub_agents = get_all_catchup2_sub_agents()
    logger.info(f"Loaded {len(sub_agents)} sub-agents: {[agent['name'] for agent in sub_agents]}")
    
    # Create the main agent instructions
    instructions = create_catchup2_instructions()
    logger.info("Created main agent instructions")
    
    # Create the deep agent using research_agent architecture
    agent = create_deep_agent(
        tools=tools,
        instructions=instructions,
        model=llm,
        subagents=sub_agents,
        state_schema=CatchUp2State
    ).with_config({"recursion_limit": 1000})
    
    logger.info("CatchUp2 agent created successfully")
    return agent




def create_catchup2_agent_with_full_tools(
    model_name: str = "anthropic/claude-3.5-sonnet",
    **kwargs
):
    """
    Create CatchUp2 agent with full functionality including MCP tools.

    This version creates the agent synchronously but includes all tools (local + MCP).
    MCP tools are loaded dynamically at runtime, following the same pattern as the
    original CatchUp agent. This provides full functionality while maintaining
    LangGraph Studio compatibility.

    Args:
        model_name: Name of the LLM model to use
        **kwargs: Additional configuration parameters

    Returns:
        Configured CatchUp2 agent ready for full marketplace operations
    """
    logger.info(f"Creating CatchUp2 agent with full tools and model: {model_name}")

    # Create the LLM instance
    llm = create_llm(model_name, **kwargs)
    logger.info("LLM instance created successfully")

    # Core research_agent tools for planning and task management
    planning_tools = [
        write_todos              # TODO list creation and task management
    ]

    # Local marketplace tools
    local_marketplace_tools = [
        get_all_categories,      # Category discovery and validation
        search_deals,            # Deal search by category name
        get_deals_by_categoryId, # Direct category-based deal retrieval
        get_deals,               # General deal access with business filtering
    ]

    # User management tools
    local_user_tools = [
        get_user_details_by_id,  # User profile information
    ]

    # MCP placeholder tools (will resolve to actual MCP tools at runtime)
    mcp_placeholder_tools = create_mcp_placeholder_tools()

    # Combine all tools: local tools + MCP placeholder tools
    tools = planning_tools + local_marketplace_tools + local_user_tools + mcp_placeholder_tools
    logger.info(f"Created {len(tools)} tools for CatchUp2 agent ({len(tools) - len(mcp_placeholder_tools)} local + {len(mcp_placeholder_tools)} MCP placeholders)")

    # Get all sub-agents (including those that use MCP tools)
    # The MCP tools will be loaded dynamically at runtime by the deepagents framework
    sub_agents = get_all_catchup2_sub_agents()
    logger.info(f"Loaded {len(sub_agents)} sub-agents: {[agent['name'] for agent in sub_agents]}")

    # Create the main agent instructions
    instructions = create_catchup2_instructions()
    logger.info("Created main agent instructions")

    # Create the deep agent using research_agent architecture
    # Note: MCP tools will be loaded dynamically when sub-agents are invoked
    agent = create_deep_agent(
        tools=tools,
        instructions=instructions,
        model=llm,
        subagents=sub_agents,
        state_schema=CatchUp2State
    ).with_config({"recursion_limit": 1000})

    logger.info("CatchUp2 agent created successfully with full functionality")
    return agent


# Factory function for LangGraph Studio compatibility
def agent():
    """
    Factory function that creates and returns a CatchUp2 agent.

    This function is called by LangGraph Studio to create the agent instance.
    It creates the agent with full functionality including MCP tools that are
    loaded dynamically at runtime, following the same pattern as the original CatchUp agent.

    Returns:
        Configured CatchUp2 agent ready for full marketplace operations
    """
    return create_catchup2_agent_with_full_tools()


# Create the default agent instance (async initialization)
async def initialize_default_agent():
    """Initialize the default CatchUp2 agent with MCP tools."""
    logger.info("Initializing default CatchUp2 agent...")
    agent_instance = await create_catchup2_agent()
    logger.info("Default CatchUp2 agent initialized and ready for use")
    return agent_instance

async def get_agent():
    """Get or create the CatchUp2 agent instance with full MCP tools."""
    # Return the full-featured agent with MCP tools
    return await create_catchup2_agent()


async def get_agent_info() -> Dict[str, Any]:
    """
    Get information about the CatchUp2 agent configuration.

    Returns:
        Dictionary containing agent configuration details
    """
    tools = await create_catchup2_tools()
    sub_agents = get_all_catchup2_sub_agents()
    
    return {
        "agent_name": "CatchUp2",
        "architecture": "research_agent with deepagents",
        "total_tools": len(tools),
        "tool_names": [tool.name for tool in tools],
        "total_sub_agents": len(sub_agents),
        "sub_agent_names": [agent["name"] for agent in sub_agents],
        "state_schema": "CatchUp2State",
        "features": [
            "Multi-tenant marketplace support",
            "Deal search and booking management",
            "Email and WhatsApp communication",
            "User profile and preference management",
            "Business information and location services",
            "JSON response formatting",
            "Multi-language support",
            "Enhanced query processing"
        ]
    }


async def validate_agent_configuration() -> Dict[str, Any]:
    """
    Validate the CatchUp2 agent configuration and dependencies.

    Returns:
        Dictionary containing validation results
    """
    validation_results = {
        "status": "success",
        "issues": [],
        "warnings": []
    }

    try:
        # Check environment variables
        required_env_vars = ["OPENROUTER_API_KEY"]
        for var in required_env_vars:
            if not os.getenv(var):
                validation_results["issues"].append(f"Missing environment variable: {var}")

        # Check tool availability
        tools = await create_catchup2_tools()
        if len(tools) == 0:
            validation_results["issues"].append("No tools available")
        
        # Check sub-agent configuration
        sub_agents = get_all_catchup2_sub_agents()
        if len(sub_agents) == 0:
            validation_results["warnings"].append("No sub-agents configured")
        
        # Check database connectivity (if applicable)
        try:
            from .supabase.client import get_supabase_client
            get_supabase_client()
            validation_results["database_connection"] = "available"
        except Exception as e:
            validation_results["warnings"].append(f"Database connection issue: {str(e)}")
        
        if validation_results["issues"]:
            validation_results["status"] = "error"
        elif validation_results["warnings"]:
            validation_results["status"] = "warning"
            
    except Exception as e:
        validation_results["status"] = "error"
        validation_results["issues"].append(f"Configuration validation failed: {str(e)}")
    
    return validation_results


# Export main components
__all__ = [
    "agent",
    "create_catchup2_agent",
    "create_catchup2_agent_with_full_tools",
    "get_agent",
    "initialize_default_agent",
    "CatchUp2State",
    "create_catchup2_state",
    "get_agent_info",
    "validate_agent_configuration"
]
