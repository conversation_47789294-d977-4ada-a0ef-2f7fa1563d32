"""
CatchUp2 Communication Tools

This module contains tools for communication operations including email sending,
WhatsApp messaging, and notification management for the marketplace platform.

All tools maintain compatibility with the existing CatchUp system while providing
enhanced functionality for user communication and engagement.
"""

import json
from typing import List, Dict, Any, Optional
from langchain_core.tools import tool
from shared.logger import logger
from ..supabase.client import get_supabase_client


@tool
def send_email_to_user(user_id: str, subject: str, content: str, email_type: str = "notification", booking_id: Optional[str] = None, deal_id: Optional[str] = None) -> str:
    """
    Send a professionally formatted HTML email to a user.
    
    This tool sends emails for various purposes including booking confirmations,
    promotional offers, notifications, and other relevant communications. It ensures
    branding consistency, personalization, and mobile-friendly design.
    
    Args:
        user_id: ID of the user to send email to
        subject: Email subject line
        content: Email content (can include HTML)
        email_type: Type of email (notification, booking, promotion, etc.)
        booking_id: Optional booking ID for booking-related emails
        deal_id: Optional deal ID for deal-related emails
        
    Returns:
        JSON string containing email sending result
    """
    try:
        logger.info(f"Sending email to user {user_id}, type: {email_type}")
        
        # Get database client
        client = get_supabase_client()
        
        # First, get user details to retrieve email address
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, email, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for email: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        user = user_result['data'][0]
        email_address = user.get('email')
        
        if not email_address:
            logger.warning(f"No email address found for user {user_id}")
            return json.dumps({"error": "User email address not available", "success": False})
        
        # Check user preferences for email communications
        preferences = user.get('preferences', {})
        if preferences.get('email_notifications') == False and email_type != 'booking':
            logger.info(f"User {user_id} has disabled email notifications")
            return json.dumps({"error": "User has disabled email notifications", "success": False})
        
        # Create email record in database
        email_data = {
            'user_id': user_id,
            'email_address': email_address,
            'subject': subject,
            'content': content,
            'email_type': email_type,
            'booking_id': booking_id,
            'deal_id': deal_id,
            'status': 'pending',
            'created_at': 'now()'
        }
        
        # Store email in database
        result = client.execute_query(
            table='email_queue',
            operation='insert',
            data=email_data
        )
        
        if result.get('error'):
            logger.error(f"Database error storing email: {result['error']}")
            return json.dumps({"error": "Failed to queue email", "success": False})
        
        # Here you would integrate with your actual email service (SendGrid, AWS SES, etc.)
        # For now, we'll simulate successful sending
        
        # Update email status to sent
        email_id = result.get('data', [{}])[0].get('id') if result.get('data') else None
        if email_id:
            client.execute_query(
                table='email_queue',
                operation='update',
                data={'status': 'sent', 'sent_at': 'now()'},
                filters=[{'column': 'id', 'value': email_id}]
            )
        
        logger.info(f"Successfully sent email to {email_address}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "email_address": email_address,
            "subject": subject,
            "email_type": email_type,
            "message": "Email sent successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in send_email_to_user: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def send_whatsapp_message(user_id: str, message: str, message_type: str = "notification", booking_id: Optional[str] = None, deal_id: Optional[str] = None) -> str:
    """
    Send a WhatsApp message to a user.
    
    This tool sends WhatsApp messages for quick notifications, booking confirmations,
    and other time-sensitive communications. It ensures messages are concise,
    friendly, and appropriate for the WhatsApp platform.
    
    Args:
        user_id: ID of the user to send message to
        message: Message content (plain text)
        message_type: Type of message (notification, booking, promotion, etc.)
        booking_id: Optional booking ID for booking-related messages
        deal_id: Optional deal ID for deal-related messages
        
    Returns:
        JSON string containing WhatsApp sending result
    """
    try:
        logger.info(f"Sending WhatsApp message to user {user_id}, type: {message_type}")
        
        # Get database client
        client = get_supabase_client()
        
        # First, get user details to retrieve phone number
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, phone, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for WhatsApp: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        user = user_result['data'][0]
        phone_number = user.get('phone')
        
        if not phone_number:
            logger.warning(f"No phone number found for user {user_id}")
            return json.dumps({
                "error": "Phone number not available. Please complete your profile in the CatchUp app.",
                "success": False
            })
        
        # Check user preferences for WhatsApp communications
        preferences = user.get('preferences', {})
        if preferences.get('whatsapp_notifications') == False and message_type != 'booking':
            logger.info(f"User {user_id} has disabled WhatsApp notifications")
            return json.dumps({"error": "User has disabled WhatsApp notifications", "success": False})
        
        # Ensure message doesn't contain sensitive IDs
        sanitized_message = message
        # Remove any potential ID references that shouldn't be visible to users
        sensitive_patterns = ['dealId', 'businessId', 'userId', 'sessionId']
        for pattern in sensitive_patterns:
            if pattern in sanitized_message:
                logger.warning(f"Removing sensitive information from WhatsApp message: {pattern}")
                # You might want to implement more sophisticated sanitization here
        
        # Create WhatsApp message record in database
        whatsapp_data = {
            'user_id': user_id,
            'phone_number': phone_number,
            'message': sanitized_message,
            'message_type': message_type,
            'booking_id': booking_id,
            'deal_id': deal_id,
            'status': 'pending',
            'created_at': 'now()'
        }
        
        # Store message in database
        result = client.execute_query(
            table='whatsapp_queue',
            operation='insert',
            data=whatsapp_data
        )
        
        if result.get('error'):
            logger.error(f"Database error storing WhatsApp message: {result['error']}")
            return json.dumps({"error": "Failed to queue WhatsApp message", "success": False})
        
        # Here you would integrate with your actual WhatsApp service (Twilio, WhatsApp Business API, etc.)
        # For now, we'll simulate successful sending
        
        # Update message status to sent
        message_id = result.get('data', [{}])[0].get('id') if result.get('data') else None
        if message_id:
            client.execute_query(
                table='whatsapp_queue',
                operation='update',
                data={'status': 'sent', 'sent_at': 'now()'},
                filters=[{'column': 'id', 'value': message_id}]
            )
        
        logger.info(f"Successfully sent WhatsApp message to {phone_number}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "phone_number": phone_number,
            "message_type": message_type,
            "message": "WhatsApp message sent successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in send_whatsapp_message: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def get_communication_preferences(user_id: str) -> str:
    """
    Get user's communication preferences.
    
    This tool retrieves the user's preferences for different types of communications
    including email, WhatsApp, and notification settings.
    
    Args:
        user_id: ID of the user to get preferences for
        
    Returns:
        JSON string containing communication preferences
    """
    try:
        logger.info(f"Fetching communication preferences for user {user_id}")
        
        # Get database client
        client = get_supabase_client()
        
        # Get user preferences
        result = client.execute_query(
            table='users',
            operation='select',
            columns='id, preferences, email, phone',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if result.get('error') or not result.get('data'):
            logger.warning(f"User not found: {user_id}")
            return json.dumps({"error": "User not found", "preferences": None})
        
        user = result['data'][0]
        preferences = user.get('preferences', {})
        
        # Extract communication-related preferences
        comm_preferences = {
            'user_id': user_id,
            'email_available': bool(user.get('email')),
            'phone_available': bool(user.get('phone')),
            'email_notifications': preferences.get('email_notifications', True),
            'whatsapp_notifications': preferences.get('whatsapp_notifications', True),
            'booking_confirmations': preferences.get('booking_confirmations', True),
            'promotional_emails': preferences.get('promotional_emails', False),
            'promotional_whatsapp': preferences.get('promotional_whatsapp', False),
            'preferred_communication': preferences.get('preferred_communication', 'email')
        }
        
        logger.info(f"Successfully fetched communication preferences for user {user_id}")
        
        return json.dumps({
            "success": True,
            "preferences": comm_preferences
        })
        
    except Exception as e:
        logger.error(f"Error in get_communication_preferences: {e}")
        return json.dumps({"error": str(e), "preferences": None})


@tool
def update_communication_preferences(user_id: str, preferences: Dict[str, Any]) -> str:
    """
    Update user's communication preferences.
    
    This tool allows users to update their communication preferences including
    email and WhatsApp notification settings.
    
    Args:
        user_id: ID of the user to update preferences for
        preferences: Dictionary containing preference updates
        
    Returns:
        JSON string containing update result
    """
    try:
        logger.info(f"Updating communication preferences for user {user_id}")
        
        # Get database client
        client = get_supabase_client()
        
        # Get current preferences
        current_result = client.execute_query(
            table='users',
            operation='select',
            columns='preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if current_result.get('error') or not current_result.get('data'):
            logger.warning(f"User not found: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        current_preferences = current_result['data'][0].get('preferences', {})
        
        # Merge communication preferences
        updated_preferences = {**current_preferences, **preferences}
        
        # Update preferences in database
        result = client.execute_query(
            table='users',
            operation='update',
            data={'preferences': updated_preferences, 'updated_at': 'now()'},
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if result.get('error'):
            logger.error(f"Database error updating preferences: {result['error']}")
            return json.dumps({"error": "Failed to update preferences", "success": False})
        
        logger.info(f"Successfully updated communication preferences for user {user_id}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "updated_preferences": preferences,
            "message": "Communication preferences updated successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in update_communication_preferences: {e}")
        return json.dumps({"error": str(e), "success": False})
